{% extends "base.html" %}

{% block title %}Gestion des Commandes en Ligne{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-shopping-cart"></i> Commandes en Ligne - {{ site.site_name }}</h2>
                <div>
                    <button class="btn btn-outline-primary" onclick="refreshOrders()">
                        <i class="fas fa-sync-alt"></i> Actualiser
                    </button>
                    <a href="{{ url_for('settings.online_ordering') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-cog"></i> Paramètres
                    </a>
                </div>
            </div>

            <!-- Statistiques rapides -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5>En attente</h5>
                                    <h3 id="pending-count">{{ orders|selectattr('status.value', 'equalto', 'pending')|list|length }}</h3>
                                </div>
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5>En préparation</h5>
                                    <h3 id="preparing-count">{{ orders|selectattr('status.value', 'equalto', 'preparing')|list|length }}</h3>
                                </div>
                                <i class="fas fa-utensils fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5>Prêtes</h5>
                                    <h3 id="ready-count">{{ orders|selectattr('status.value', 'equalto', 'ready')|list|length }}</h3>
                                </div>
                                <i class="fas fa-check fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5>Aujourd'hui</h5>
                                    <h3>{{ today_orders_count }}</h3>
                                </div>
                                <i class="fas fa-calendar-day fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filtres -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="statusFilter" class="form-label">Statut</label>
                            <select class="form-select" id="statusFilter" onchange="filterOrders()">
                                <option value="">Tous les statuts</option>
                                <option value="pending">En attente</option>
                                <option value="confirmed">Confirmée</option>
                                <option value="preparing">En préparation</option>
                                <option value="ready">Prête</option>
                                <option value="out_for_delivery">En livraison</option>
                                <option value="delivered">Livrée</option>
                                <option value="cancelled">Annulée</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="typeFilter" class="form-label">Type</label>
                            <select class="form-select" id="typeFilter" onchange="filterOrders()">
                                <option value="">Tous les types</option>
                                <option value="delivery">Livraison</option>
                                <option value="pickup">À emporter</option>
                                <option value="dine_in">Sur place</option>
                                <option value="drive_through">Drive</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="dateFilter" class="form-label">Date</label>
                            <input type="date" class="form-control" id="dateFilter" onchange="filterOrders()">
                        </div>
                        <div class="col-md-3">
                            <label for="searchFilter" class="form-label">Recherche</label>
                            <input type="text" class="form-control" id="searchFilter" placeholder="N° commande, client..." onkeyup="filterOrders()">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Liste des commandes -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="ordersTable">
                            <thead>
                                <tr>
                                    <th>N° Commande</th>
                                    <th>Client</th>
                                    <th>Type</th>
                                    <th>Statut</th>
                                    <th>Montant</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr data-order-id="{{ order.id }}" 
                                    data-status="{{ order.status.value }}" 
                                    data-type="{{ order.order_type.value }}"
                                    data-date="{{ order.ordered_at.strftime('%Y-%m-%d') }}"
                                    data-search="{{ order.order_number }} {{ order.customer.first_name }} {{ order.customer.last_name }}">
                                    <td>
                                        <strong>#{{ order.order_number }}</strong>
                                        {% if order.customer_notes %}
                                            <br><small class="text-muted"><i class="fas fa-comment"></i> {{ order.customer_notes[:50] }}...</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ order.customer.first_name }} {{ order.customer.last_name }}
                                        {% if order.customer.phone %}
                                            <br><small class="text-muted">{{ order.customer.phone }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {% if order.order_type.value == 'delivery' %}
                                                <i class="fas fa-truck"></i> Livraison
                                            {% elif order.order_type.value == 'pickup' %}
                                                <i class="fas fa-shopping-bag"></i> À emporter
                                            {% elif order.order_type.value == 'dine_in' %}
                                                <i class="fas fa-utensils"></i> Sur place
                                            {% elif order.order_type.value == 'drive_through' %}
                                                <i class="fas fa-car"></i> Drive
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        <select class="form-select form-select-sm status-select" 
                                                data-order-id="{{ order.id }}" 
                                                onchange="updateOrderStatus({{ order.id }}, this.value)">
                                            <option value="pending" {% if order.status.value == 'pending' %}selected{% endif %}>En attente</option>
                                            <option value="confirmed" {% if order.status.value == 'confirmed' %}selected{% endif %}>Confirmée</option>
                                            <option value="preparing" {% if order.status.value == 'preparing' %}selected{% endif %}>En préparation</option>
                                            <option value="ready" {% if order.status.value == 'ready' %}selected{% endif %}>Prête</option>
                                            <option value="out_for_delivery" {% if order.status.value == 'out_for_delivery' %}selected{% endif %}>En livraison</option>
                                            <option value="delivered" {% if order.status.value == 'delivered' %}selected{% endif %}>Livrée</option>
                                            <option value="cancelled" {% if order.status.value == 'cancelled' %}selected{% endif %}>Annulée</option>
                                        </select>
                                    </td>
                                    <td>
                                        <strong>{{ "%.2f"|format(order.total_amount) }}€</strong>
                                        {% if order.delivery_fee > 0 %}
                                            <br><small class="text-muted">+ {{ "%.2f"|format(order.delivery_fee) }}€ livraison</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ order.ordered_at.strftime('%d/%m/%Y %H:%M') }}
                                        {% if order.requested_delivery_time %}
                                            <br><small class="text-info">Demandée: {{ order.requested_delivery_time.strftime('%H:%M') }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="viewOrderDetails({{ order.id }})">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" onclick="printOrder({{ order.id }})">
                                                <i class="fas fa-print"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour les détails de commande -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails de la commande</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderDetailsContent">
                <!-- Le contenu sera chargé dynamiquement -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateOrderStatus(orderId, newStatus) {
    fetch(`/online-ordering/admin/orders/${orderId}/update_status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            order_id: orderId,
            status: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showNotification('Statut mis à jour avec succès', 'success');
            // Mettre à jour les compteurs
            updateCounters();
        } else {
            showNotification('Erreur: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('Erreur de connexion', 'error');
    });
}

function filterOrders() {
    const statusFilter = document.getElementById('statusFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;
    const dateFilter = document.getElementById('dateFilter').value;
    const searchFilter = document.getElementById('searchFilter').value.toLowerCase();
    
    const rows = document.querySelectorAll('#ordersTable tbody tr');
    
    rows.forEach(row => {
        const status = row.dataset.status;
        const type = row.dataset.type;
        const date = row.dataset.date;
        const search = row.dataset.search.toLowerCase();
        
        let show = true;
        
        if (statusFilter && status !== statusFilter) show = false;
        if (typeFilter && type !== typeFilter) show = false;
        if (dateFilter && date !== dateFilter) show = false;
        if (searchFilter && !search.includes(searchFilter)) show = false;
        
        row.style.display = show ? '' : 'none';
    });
}

function refreshOrders() {
    location.reload();
}

function updateCounters() {
    // Compter les commandes visibles par statut
    const rows = document.querySelectorAll('#ordersTable tbody tr[style=""]');
    const counts = {
        pending: 0,
        preparing: 0,
        ready: 0
    };
    
    rows.forEach(row => {
        const status = row.dataset.status;
        if (counts.hasOwnProperty(status)) {
            counts[status]++;
        }
    });
    
    document.getElementById('pending-count').textContent = counts.pending;
    document.getElementById('preparing-count').textContent = counts.preparing;
    document.getElementById('ready-count').textContent = counts.ready;
}

function viewOrderDetails(orderId) {
    // Charger les détails de la commande
    fetch(`/online-ordering/admin/orders/${orderId}/details`)
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            document.getElementById('orderDetailsContent').innerHTML = data.html;
            new bootstrap.Modal(document.getElementById('orderDetailsModal')).show();
        }
    });
}

function printOrder(orderId) {
    window.open(`/online-ordering/admin/orders/${orderId}/print`, '_blank');
}

function showNotification(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 'alert-info';
    
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Auto-refresh toutes les 30 secondes
setInterval(refreshOrders, 30000);
</script>
{% endblock %}
